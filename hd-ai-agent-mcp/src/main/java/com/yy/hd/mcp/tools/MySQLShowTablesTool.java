package com.yy.hd.mcp.tools;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.mysql.MySQLReader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class MySQLShowTablesTool {

    private final MySQLReader mysqlReader;

    private static final String SHOW_TABLES_LIKE = "show tables like ";

    @Tool(name = "MySQLShowTablesTool", description = "模糊匹配mysql数据表")
    public String mysqlShowTablesTool(String table) {
        String sql = SHOW_TABLES_LIKE + "'%" + table + "%'";

        try {
            List<String> datas = mysqlReader.read(sql);
            return JsonUtils.toJson(datas);
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("mysqlShowTablesTool error", e);
        }
        return "模糊匹配mysql数据表失败";
    }
}
