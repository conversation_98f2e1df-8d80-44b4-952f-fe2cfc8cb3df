package com.yy.hd.mcp.tools;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.mysql.MySQLReader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class MySQLQueryTool {

    private final MySQLReader mysqlReader;

    @Tool(name = "MySQLQueryTool", description = "执行mysql查询语句")
    public String mysqlQueryTool(String sql) {
        try {
            List<Document> documents = mysqlReader.read(sql);
            return JsonUtils.toJson(documents);
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("mysqlQueryTool error", e);
        }
        return "查询mysql数据失败";
    }
}
