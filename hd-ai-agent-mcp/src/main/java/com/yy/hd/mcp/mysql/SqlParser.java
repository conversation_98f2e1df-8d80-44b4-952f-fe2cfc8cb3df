package com.yy.hd.mcp.mysql;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.parser.SimpleNode;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.UnsupportedStatement;
import net.sf.jsqlparser.statement.UseStatement;
import net.sf.jsqlparser.statement.select.Limit;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.show.ShowTablesStatement;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SqlParser {

    public static Map<String, List<String>> parseSql(String sql) {
        Map<String, List<String>> sqlMap = new HashMap<>();
        try {
            List<Statement> statements = CCJSqlParserUtil.parseStatements(sql);
            for (Statement stmt : statements) {
                if (stmt instanceof SetOperationList setOperationList) {
                    List<Select> selects = setOperationList.getSelects();
                    for (Select select : selects) {
                        PlainSelect plainSelect = select.getPlainSelect();
                        SimpleNode simpleNode = plainSelect.getFromItem().getASTNode();
                        if (StringUtils.equalsIgnoreCase(simpleNode.jjtGetFirstToken().toString(), simpleNode.jjtGetLastToken().toString())) {
                            throw new IllegalArgumentException("请指定数据库：" + stmt);
                        }
                        String database = simpleNode.jjtGetFirstToken().toString();
                        plainSelect = rewriteSql(plainSelect);
                        checkSelect(plainSelect);
                        sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                                .add(plainSelect.toString());
                    }

                } else if (stmt instanceof PlainSelect plainSelect) {
                    SimpleNode simpleNode = plainSelect.getFromItem().getASTNode();
                    if (StringUtils.equalsIgnoreCase(simpleNode.jjtGetFirstToken().toString(), simpleNode.jjtGetLastToken().toString())) {
                        throw new IllegalArgumentException("请指定数据库：" + stmt);
                    }
                    String database = simpleNode.jjtGetFirstToken().toString();
                    plainSelect = rewriteSql(plainSelect);
                    checkSelect(plainSelect);
                    sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                            .add(plainSelect.toString());
                }
            }
        } catch (JSQLParserException e) {
            throw new IllegalArgumentException("SQL语法错误：" + sql);
        }
        return sqlMap;
    }

    private static PlainSelect rewriteSql(PlainSelect plainSelect) {
        int maxLimit = 100;
        Limit limit = plainSelect.getLimit();
        if (limit == null) {
            limit = new Limit();
            limit.withRowCount(new LongValue(maxLimit));
            plainSelect.setLimit(limit);
        } else {
            if (limit.getRowCount(LongValue.class).getValue() > maxLimit) {
                limit.withRowCount(new LongValue(maxLimit));
            }
        }
        return plainSelect;
    }

    private static void checkSelect(PlainSelect plainSelect) {
        if (plainSelect.getForMode() != null || plainSelect.getForUpdateTable() != null) {
            throw new IllegalArgumentException("不支持FOR UPDATE：" + plainSelect);
        }
        Limit limit = plainSelect.getLimit();
        if (limit == null) {
            throw new IllegalArgumentException("SQL语句必须包含limit：" + plainSelect);
        }
    }

    public static void main(String[] args) {
        String sql = "select * from zhuiya.t_user where uid = 1 limit 101 union select * from zhuiya_task.t_task where uid = 1 limit 101";
        System.out.println(parseSql(sql));
    }
}
