package com.yy.hd.mcp.mysql;

import com.alibaba.cloud.ai.reader.mysql.MySQLDocumentReader;
import com.alibaba.cloud.ai.reader.mysql.MySQLResource;
import org.springframework.ai.document.Document;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class MySQLReader {

    private final Binder binder;

    public MySQLReader(Environment environment) {
        this.binder = Binder.get(environment);
    }

    public List<String> databaseList() {
        Map<String, DatasourceProperties> datasourcePropertiesMap = binder.bindOrCreate("spring.datasource", Map.class)
                .values()
                .stream()
                .map(properties -> properties)
                .toList();
    }

    public List<String> read(String query) {
        List<Document> result = new ArrayList<>();
        Map<String, List<String>> sqlMap = SqlParser.parseSql(query);
        for (Map.Entry<String, List<String>> entry : sqlMap.entrySet()) {
            String database = entry.getKey();
            List<String> sqlList = entry.getValue();
            DatasourceProperties datasourceProperties = binder.bindOrCreate("spring.datasource." + database, DatasourceProperties.class);
            for (String sql : sqlList) {
                MySQLResource mySQLResource = new MySQLResource(datasourceProperties.getHost(), datasourceProperties.getPort(),
                        datasourceProperties.getDatabase(), datasourceProperties.getUsername(),
                        datasourceProperties.getPassword(), sql, null, null);
                result.addAll(new MySQLDocumentReader(mySQLResource).read());
            }
        }
        return result.stream()
                .map(Document::getText)
                .toList();
    }

}
