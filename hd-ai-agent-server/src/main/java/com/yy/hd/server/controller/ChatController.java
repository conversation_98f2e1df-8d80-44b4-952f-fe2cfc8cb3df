package com.yy.hd.server.controller;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.exception.GraphRunnerException;
import com.yy.hd.commons.AgentChatReq;
import com.yy.hd.commons.AgentHttpHeaderKeys;
import com.yy.hd.model.ChatClientSelector;
import com.yy.hd.server.dto.ToolDTO;
import com.yy.hd.server.graph.GraphProcess;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import com.yy.hd.server.service.McpToolService;
import com.yy.hd.server.service.PromptService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class ChatController {

    @Resource
    private CompiledGraph workflowGraph;

    @Resource
    private McpToolService mcpToolService;

    @Resource
    private ChatClientSelector chatClientSelector;

    @Resource
    private PromptService promptService;

    private static final String RANKING_KEY = McpToolService.RANKING_KEY;

    @GetMapping(value = "/tools")
    public Flux<ToolDTO> tools() {
        return Flux.fromIterable(mcpToolService.tools())
                .publishOn(Schedulers.boundedElastic());
    }

    @PostMapping(value = "/chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> stream(@RequestHeader(AgentHttpHeaderKeys.SOURCE_HEADER) String source,
                                              @CookieValue(value = "yyuid", required = false, defaultValue = "") String uid,
                                              @RequestBody AgentChatReq req)
            throws GraphRunnerException {
        WorkflowContext workflowContext = buildWorkflowContext(source, uid, req);
        Map<String, Object> inputs = Map.of(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext);
        GraphProcess graphProcess = new GraphProcess();
        if (req.getStream()) {
            Sinks.Many<ServerSentEvent<String>> sink = Sinks.many().unicast().onBackpressureBuffer();
            AsyncGenerator<NodeOutput> resultFuture = workflowGraph.stream(inputs);
            graphProcess.processStream(resultFuture, sink);
            return sink.asFlux()
                    // 必须设置buffer输出，避免频繁触发next事件，导致StackOverflowError
                    .bufferTimeout(5, Duration.ofMillis(500))
                    .flatMapIterable(list -> list)
                    .doOnComplete(() -> log.info("stream completed"))
                    .doOnCancel(() -> log.info("client disconnected from stream"))
                    .onErrorResume(e -> {
                        log.error("error occurred during stream", e);
                        return Flux.just(ServerSentEvent
                                        .builder("服务器响应失败，请稍后重试！")
                                        .build()
                        );
                    });
        } else {
            return Mono.just(inputs)
                    .publishOn(Schedulers.boundedElastic())
                    .map(s -> {
                        try {
                            return workflowGraph.invoke(inputs);
                        } catch (GraphRunnerException e) {
                            throw new RuntimeException(e);
                        }
                    })
                    .map(overAllStateOptional -> {
                        String output = overAllStateOptional.map(overAllState ->
                                        (String) overAllState.value(WorkflowRegisterKeys.MESSAGES_KEY.getKey())
                                                .orElse("服务器响应失败"))
                                .orElse("服务器响应失败");
                        Sinks.One<ServerSentEvent<String>> sink = Sinks.one();
                        sink.tryEmitValue(ServerSentEvent.builder(output).build());
                        return sink;
                    })
                    .flatMap(Sinks.Empty::asMono)
                    .flux()
                    .doOnComplete(() -> log.info("stream completed"))
                    .doOnCancel(() -> log.info("client disconnected from stream"))
                    .onErrorResume(e -> {
                        log.error("error occurred during stream", e);
                        return Flux.just(ServerSentEvent
                                .builder("服务器响应失败，请稍后重试！")
                                .build()
                        );
                    });
        }
    }

    @PostMapping(value = "/chat")
    public Flux<String> chat(@RequestHeader(AgentHttpHeaderKeys.SOURCE_HEADER) String source,
                             @CookieValue(value = "yyuid", required = false, defaultValue = "") String uid,
                             @RequestBody AgentChatReq req)
            throws GraphRunnerException {
        WorkflowContext workflowContext = buildWorkflowContext(source, uid, req);
        Map<String, Object> inputs = Map.of(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext);
        return Mono.just(inputs)
                .publishOn(Schedulers.boundedElastic())
                .map(s -> {
                    try {
                        return workflowGraph.invoke(inputs);
                    } catch (GraphRunnerException e) {
                        throw new RuntimeException(e);
                    }
                })
                .map(overAllStateOptional -> overAllStateOptional
                        .map(overAllState ->
                                (String) overAllState.value(WorkflowRegisterKeys.MESSAGES_KEY.getKey())
                                        .orElse("服务器响应失败"))
                        .orElse("服务器响应失败"))
                .flux()
                .doOnComplete(() -> log.info("chat completed"))
                .doOnCancel(() -> log.info("client disconnected from chat"))
                .onErrorResume(e -> {
                    log.error("error occurred during chat", e);
                    return Flux.just("服务器响应失败，请稍后重试！");
                });
    }

    private WorkflowContext buildWorkflowContext(String source, String uid, AgentChatReq chatReq) {
        uid = StringUtils.isNoneBlank(uid) ? uid : chatReq.getUid();
        return WorkflowContext.builder()
                .source(source)
                .uid(uid)
                .chatId(chatReq.getChatId())
                .input(chatReq.getMessage())
                .rankingKey(RANKING_KEY)
                .toolNames(StringUtils.isBlank(chatReq.getTool()) ? null : List.of(chatReq.getTool()))
                .build();
    }

}