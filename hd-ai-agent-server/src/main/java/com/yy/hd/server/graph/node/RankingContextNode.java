package com.yy.hd.server.graph.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.yy.hd.server.graph.BM25SearchContext;
import com.yy.hd.server.graph.SimilaritySearchContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import com.yy.hd.server.graph.RankingContext;
import com.yy.hd.server.graph.SourceType;
import com.yy.hd.server.graph.WorkflowContext;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * 召回/重排上下文设置
 */
@Component
public class RankingContextNode implements NodeAction {

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey()).orElse(null);
        setSearchRankingContent(workflowContext);
        return Map.of(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext);
    }

    private void setSearchRankingContent(WorkflowContext workflowContext) {
        SourceType sourceType = SourceType.fromType(workflowContext.getSource());
        String toolName = Optional.ofNullable(workflowContext.getToolNames())
                .map(tools -> {
                    if (CollectionUtils.isNotEmpty(tools)) {
                        return tools.getFirst();
                    }
                    return null;
                })
                .orElse(null);
        String filterExpression = filterExpression(toolName);
        switch (sourceType) {
            case WEB -> {
                workflowContext.setBm25SearchContext(new BM25SearchContext(5));
                workflowContext.setSimilaritySearchContext(new SimilaritySearchContext(0.0, 10, filterExpression));
                workflowContext.setRankingContext(new RankingContext(0.0, 5));
            }
            case INFOFLOW -> {
                workflowContext.setBm25SearchContext(new BM25SearchContext(2));
                workflowContext.setSimilaritySearchContext(new SimilaritySearchContext(0.3, 4, filterExpression));
                workflowContext.setRankingContext(new RankingContext(0.3, 1));
            }
            // 前面已经有条件边限制了，正常是不会执行到这里的
            default -> {
                workflowContext.setBm25SearchContext(new BM25SearchContext(1));
                workflowContext.setSimilaritySearchContext(new SimilaritySearchContext(0.0, 1, filterExpression));
                workflowContext.setRankingContext(new RankingContext(1.0, 1));
            }
        }
    }

    private String filterExpression(String toolName) {
        StringBuilder expression = new StringBuilder();
        if (StringUtils.isNotBlank(toolName)) {
            expression.append(WorkflowRegisterKeys.TOOL_NAME_METADATA_KEY.getKey()).append(" == '").append(toolName).append("'");
        }
        return expression.toString();
    }
}
