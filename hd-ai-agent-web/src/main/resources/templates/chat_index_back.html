<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>秒答</title>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <link rel="icon" type="image/png" href="https://peiwan.bs2dl.yy.com/c0df986ba919482cb41e59352bd8f383.png">
  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #0f0f0f;
    }
    textarea:focus {
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
    }
    .message-content {
      text-align: left;
    }
    .user-content {
      white-space: pre-wrap;
      word-break: break-word;
      overflow-wrap: break-word;
    }
    .markdown-content p {
      margin-bottom: 1.25rem
    }
    #messagesWrapper {
      width: 100%;
      max-width: 100%;
      overflow-x: hidden;
    }
    
    /* 添加表格样式支持 */
    .markdown-content table {
      border-collapse: collapse;
      margin: 1rem 0;
      width: 100%;
      overflow-x: auto;
      display: block;
    }
    
    .markdown-content th,
    .markdown-content td {
      border: 1px solid #374151;
      padding: 0.5rem;
      text-align: left;
    }
    
    .markdown-content th {
      background-color: #1f2937;
    }
    
    /* 添加图片样式支持 */
    .markdown-content img {
      max-width: 100%;
      height: auto;
      border-radius: 0.3rem;
      margin: 1rem 0;
    }
    
    /* 添加引用块样式 */
    .markdown-content blockquote {
      border-left: 4px solid #3b82f6;
      padding-left: 1rem;
      margin: 1rem 0;
      color: #9ca3af;
    }
    .markdown-content p {
      margin-bottom: 1.25rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
    .markdown-content li {
      margin-bottom: 0.5rem;
    }
    .markdown-content strong {
      font-weight: 600;
    }
    .markdown-content code {
      background-color: #1f2937;
      padding: 0.1rem 0.3rem;
      border-radius: 0.2rem;
      font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
    }
    .markdown-content pre {
      background-color: #1f2937;
      padding: 0.75rem;
      border-radius: 0.3rem;
      overflow-x: auto;
      margin: 1rem 0;
      white-space: pre-wrap;
      max-width: 100%;
    }
    .markdown-content pre code {
      background-color: transparent;
      padding: 0;
    }
    /* Grok风格的消息气泡 */
    .message-bubble {
      border-radius: 1.25rem;
      padding: 1.25rem 1.5rem;
      margin-bottom: 1.5rem;
      line-height: 1.7;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      word-break: break-word;
      overflow-wrap: break-word;
    }

    .user-message {
      background-color: #1a1a1a;
      color: #ffffff;
      border: 1px solid #2a2a2a;
      align-self: flex-end;
      max-width: 75%; /* 用户消息保持较小宽度 */
    }

    .assistant-message {
      background-color: #1a1a1a;
      color: #ffffff;
      border: 1px solid #2a2a2a;
      align-self: flex-start;
      width: 100%; /* AI消息使用全宽 */
      max-width: none; /* 移除最大宽度限制 */
    }
    
    /* Grok风格的侧边栏 */
    .sidebar {
      background-color: #0f0f0f;
      border-right: 1px solid #2a2a2a;
    }
    
    .chat-item {
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }
    
    .chat-item:hover {
      background-color: #1a1a1a;
    }
    
    .chat-item.active {
      background-color: #1a1a1a;
      border-left: 3px solid #3b82f6;
    }
    
    /* Grok风格的输入框 */
    .input-container {
      background-color: #1a1a1a;
      border: 1px solid #2a2a2a;
      border-radius: 1rem;
      transition: all 0.2s ease;
    }
    
    .input-container:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    
    .typing-indicator {
      display: inline-flex;
      align-items: center;
    }
    .typing-indicator span {
      height: 8px;
      width: 8px;
      margin: 0 1px;
      background-color: #a3a3a3;
      border-radius: 50%;
      display: inline-block;
      animation: bounce 1.4s infinite ease-in-out both;
    }
    .typing-indicator span:nth-child(1) {
      animation-delay: -0.32s;
    }
    .typing-indicator span:nth-child(2) {
      animation-delay: -0.16s;
    }
    @keyframes bounce {
      0%, 80%, 100% { transform: scale(0); }
      40% { transform: scale(1); }
    }
    .input-container {
      border: 1px solid #374151;
      transition: all 0.2s ease;
    }
    .input-container:focus-within {
      border-color: #3b82f6;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
    .send-btn {
      transition: all 0.2s ease;
    }
    .send-btn:hover {
      transform: translateY(-1px);
    }
    .send-btn:active {
      transform: translateY(1px);
    }

    /* Grok风格的输入框内联网搜索按钮 */
    .input-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .web-search-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: 0.5rem;
      background: transparent;
      border: 1px solid #374151;
      cursor: pointer;
      transition: all 0.2s ease;
      color: #6b7280;
      position: relative;
      flex-shrink: 0;
    }

    .web-search-btn:hover {
      background: #1a1a1a;
      border-color: #4b5563;
      color: #9ca3af;
    }

    .web-search-btn.active {
      background: rgba(59, 130, 246, 0.1);
      border-color: #3b82f6;
      color: #3b82f6;
    }

    .web-search-btn.active::after {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 8px;
      height: 8px;
      background: #3b82f6;
      border-radius: 50%;
      border: 2px solid #1a1a1a;
    }

    .web-search-icon {
      width: 16px;
      height: 16px;
    }

    /* 工具提示 */
    .tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: #0f0f0f;
      color: #ffffff;
      padding: 0.5rem 0.75rem;
      border-radius: 0.5rem;
      font-size: 0.75rem;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      z-index: 1000;
      border: 1px solid #374151;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      margin-bottom: 0.5rem;
    }

    .tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: #374151;
    }

    .web-search-btn:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }
  </style>
</head>
<body class="bg-black text-gray-100">
  <!-- Thymeleaf变量显示 -->
  <div th:if="${message}" class="hidden" id="serverMessage" th:text="${message}"></div>
  
  <div class="flex h-screen bg-black">
    <!-- 侧边栏 - Grok风格 -->
    <div class="w-72 sidebar p-4 flex flex-col">
      <button
        id="newChatBtn"
        class="mb-6 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 text-sm flex items-center justify-center shadow-md hover:shadow-lg"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        新建对话
      </button>
      <div id="chatList" class="flex-1 overflow-y-auto space-y-1.5 pr-1">
        <!-- 聊天列表将通过JavaScript动态生成 -->
        <div class="text-gray-500 text-sm text-center mt-8" id="noChatMessage">
          没有对话记录
        </div>
      </div>
    </div>

    <!-- 主聊天区域 - Grok风格 -->
    <div class="flex-1 flex flex-col bg-black">
      <!-- 添加退出登录按钮到右上角 -->
      <div class="flex justify-end p-4">
        <button
          id="logoutBtn"
          class="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-3 rounded-lg transition-all duration-200 text-sm flex items-center justify-center"
          title="退出登录"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          退出登录
        </button>
      </div>
      <div class="flex-1 overflow-y-auto p-4 md:p-6" id="messageContainer">
        <div id="emptyState" class="flex flex-col items-center justify-center h-full text-gray-400">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-gray-700" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <p class="text-xl font-medium mb-2">开始新的对话</p>
          <p class="text-sm text-gray-500 max-w-md text-center">
            输入您的问题，开始与秒答对话
          </p>
        </div>
        <div id="messagesWrapper" class="max-w-3xl mx-auto hidden">
          <!-- 消息将通过JavaScript动态生成 -->
        </div>
      </div>
      <div class="p-4 bg-black">
        <div class="max-w-3xl mx-auto">
          <div class="mb-4">
            <label for="toolSelect" class="block text-sm font-medium text-gray-300 mb-1">选择工具:</label>
            <select id="toolSelect" class="block w-full bg-gray-800 border border-gray-700 rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="">-- 请选择一个工具 --</option>
            </select>
          </div>
          <form id="messageForm" class="input-container p-2 flex items-end">
            <textarea
              id="messageInput"
              class="flex-1 bg-transparent text-gray-100 p-2 text-base focus:outline-none resize-none min-h-[60px] max-h-[200px] placeholder-gray-500"
              placeholder="输入您的问题..."
              rows="2"
            ></textarea>
            <div class="input-actions">
              <!-- Grok风格的联网搜索按钮 -->
              <button
                type="button"
                id="webSearchBtn"
                class="web-search-btn"
                title="联网搜索"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="web-search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0 9c-1.657 0-3-4.03-3-9s1.343-9 3-9m0 9c1.657 0 3 4.03 3 9s-1.343 9-3 9m-9 9a9 9 0 019-9" />
                </svg>
                <div class="tooltip" id="webSearchTooltip">联网搜索：已关闭</div>
              </button>

              <button
                type="submit"
                id="sendButton"
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-full transition-colors ml-2 send-btn shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 flex items-center justify-center"
                disabled
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                </svg>
              </button>
              <button
                type="button"
                id="stopButton"
                class="bg-red-500 hover:bg-red-600 text-white font-medium p-2 rounded-md transition-colors ml-2 hidden"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </form>
          <div class="text-xs text-gray-500 text-center mt-2">
            按 Enter 发送，Shift + Enter 换行
          </div>
        </div>
      </div>
    </div>
  </div>

  <script th:inline="javascript">
    // 配置 marked 以支持单个换行和更宽松的 Markdown 解析
    marked.setOptions({
      breaks: true, // 单个换行符视为 <br>
      gfm: true, // 启用 GitHub Flavored Markdown
      highlight: function(code, lang) {
        return code;
      }
    });

    // 获取服务器传递的消息（如果有）
    const serverMessage = document.getElementById('serverMessage')?.textContent;
    if (serverMessage) {
      console.log('Server message:', serverMessage);
    }

    // 应用状态
    const state = {
      chats: [],
      currentChat: null,
      messages: [],
      isStreaming: false,
      controller: null
    };

    // 联网搜索状态
    const webSearchState = {
      enabled: false
    };

    // DOM元素
    const elements = {
      newChatBtn: document.getElementById('newChatBtn'),
      chatList: document.getElementById('chatList'),
      noChatMessage: document.getElementById('noChatMessage'),
      messageContainer: document.getElementById('messageContainer'),
      emptyState: document.getElementById('emptyState'),
      messagesWrapper: document.getElementById('messagesWrapper'),
      messageForm: document.getElementById('messageForm'),
      messageInput: document.getElementById('messageInput'),

      sendButton: document.getElementById('sendButton'),
      stopButton: document.getElementById('stopButton'),
      logoutBtn: document.getElementById('logoutBtn'),
      toolSelect: document.getElementById('toolSelect'),
      // 联网搜索元素
      webSearchBtn: document.getElementById('webSearchBtn'),
      webSearchTooltip: document.getElementById('webSearchTooltip')
    };

    // 获取指定名称的 cookie 值
    function getCookie(name) {
      const nameEQ = name + "=";
      const ca = document.cookie.split(';');
      for(let i=0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
      }
      return '你';
    }

    // 创建新对话的函数
    function createNewChat(title = '新对话') {
      return {
        id: Date.now().toString(),
        title,
        messages: [],
        createdAt: new Date().toISOString()
      };
    }

    // 格式化时间
    function formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // 初始化应用
    function initApp() {
      // 加载保存的聊天记录
      const savedChats = JSON.parse(localStorage.getItem('chats') || '[]');
      state.chats = savedChats;

      // 如果有聊天记录，选择第一个
      if (savedChats.length > 0) {
        state.currentChat = savedChats[0].id;
        state.messages = savedChats[0].messages;
      }

      // 加载联网搜索状态
      const savedWebSearchState = localStorage.getItem('webSearchEnabled');
      if (savedWebSearchState !== null) {
        webSearchState.enabled = JSON.parse(savedWebSearchState);
        updateWebSearchUI();
      }


      // 渲染UI
      renderChatList();
      renderMessages();

      // 设置事件监听器
      setupEventListeners();

      // 确保滚动到底部
      setTimeout(() => {
        elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
      }, 100);

      // 加载工具列表
      loadTools();
    }

    // 加载工具列表
    async function loadTools() {
      try {
        const response = await fetch('/api/tools');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const tools = await response.json();
        tools.forEach(tool => {
          const option = document.createElement('option');
          option.value = tool.name;
          option.textContent = tool.name + ' - ' + tool.description;
          elements.toolSelect.appendChild(option);
        });
      } catch (error) {
        console.error('Error loading tools:', error);
        // 可以添加错误提示给用户
      }
    }

    // 切换联网搜索状态
    function toggleWebSearch() {
      webSearchState.enabled = !webSearchState.enabled;
      updateWebSearchUI();
      saveWebSearchState();
    }

    // 更新联网搜索UI
    function updateWebSearchUI() {
      elements.webSearchBtn.classList.toggle('active', webSearchState.enabled);
      elements.webSearchTooltip.textContent = `联网搜索：${webSearchState.enabled ? '已开启' : '已关闭'}`;
      elements.webSearchBtn.title = `联网搜索：${webSearchState.enabled ? '已开启' : '已关闭'}`;
    }

    // 保存联网搜索状态
    function saveWebSearchState() {
      localStorage.setItem('webSearchEnabled', JSON.stringify(webSearchState.enabled));
    }



    // 渲染聊天列表
    function renderChatList() {
      // 清空现有列表
      elements.chatList.innerHTML = '';
      
      if (state.chats.length === 0) {
        // 显示"没有对话记录"消息
        elements.noChatMessage.classList.remove('hidden');
        return;
      }
      
      // 隐藏"没有对话记录"消息
      elements.noChatMessage.classList.add('hidden');
      
      // 添加新的聊天项
      state.chats.forEach(chat => {
        const chatItem = document.createElement('div');
        chatItem.className = `py-2.5 px-3 rounded-lg cursor-pointer text-sm flex justify-between items-center chat-item ${
          state.currentChat === chat.id ? 'active text-blue-400 pl-2' : 'hover:bg-gray-800/50 text-gray-300'
        } transition-all duration-200`;
        chatItem.dataset.chatId = chat.id;
        
        chatItem.innerHTML = `
          <div class="flex items-center truncate flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <span class="truncate">${chat.title}</span>
          </div>
          <button 
            class="ml-1 text-gray-500 hover:text-red-500 p-1 rounded-full hover:bg-gray-700/50 delete-btn"
            title="删除对话"
            data-chat-id="${chat.id}"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        `;
        
        elements.chatList.appendChild(chatItem);
      });
    }

    // 渲染消息
    function renderMessages() {
      // 清空现有消息
      elements.messagesWrapper.innerHTML = '';
      
      if (!state.messages || state.messages.length === 0) {
        // 显示空状态
        elements.emptyState.classList.remove('hidden');
        elements.messagesWrapper.classList.add('hidden');
        return;
      }
      
      // 隐藏空状态，显示消息容器
      elements.emptyState.classList.add('hidden');
      elements.messagesWrapper.classList.remove('hidden');
      
      // 添加新的消息
      state.messages.forEach(message => {
        const messageElement = document.createElement('div');
        messageElement.className = `flex flex-col mb-6 ${message.role === 'user' ? 'items-end' : 'items-start'}`;
        
        const headerElement = document.createElement('div');
        headerElement.className = `flex items-center mb-2 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`;
        
        const avatarElement = document.createElement('div');
        avatarElement.className = `w-8 h-8 rounded-full flex items-center justify-center ${
          message.role === 'user' ? 'ml-2 bg-blue-600' : 'mr-2 bg-purple-600'
        }`;
        avatarElement.textContent = message.role === 'user' ? '👤' : '🤖';
        
        const nameElement = document.createElement('div');
        nameElement.className = 'text-sm font-medium';
        if (message.role === 'user') {
          const username = getCookie('username'); // 假设用户名为 'username' 的 cookie
          nameElement.textContent = username ? username : '你';
        } else {
          nameElement.textContent = '秒答';
        }
        
        const timeElement = document.createElement('div');
        timeElement.className = `text-xs text-gray-500 ${message.role === 'user' ? 'mr-2' : 'ml-2'}`;
        timeElement.textContent = formatTime(message.timestamp);
        
        headerElement.appendChild(avatarElement);
        headerElement.appendChild(nameElement);
        headerElement.appendChild(timeElement);
        
        const bubbleElement = document.createElement('div');
        bubbleElement.className = `message-bubble ${
          message.role === 'user' ? 'user-message mr-10' : 'assistant-message ml-10'
        }`;
        
        if (message.role === 'assistant' && message.content === '' && state.isStreaming) {
          const typingIndicator = document.createElement('div');
          typingIndicator.className = 'typing-indicator';
          typingIndicator.innerHTML = '<span></span><span></span><span></span>';
          bubbleElement.appendChild(typingIndicator);
        } else if (message.role === 'assistant') {
          const contentElement = document.createElement('div');
          contentElement.className = 'markdown-content';
          contentElement.innerHTML = marked.parse(message.content);
          bubbleElement.appendChild(contentElement);
        } else {
          const contentElement = document.createElement('div');
          contentElement.className = 'user-content';
          contentElement.innerHTML = message.content.replace(/\n/g, '<br>');
          
          bubbleElement.appendChild(contentElement);
        }
        
        messageElement.appendChild(headerElement);
        messageElement.appendChild(bubbleElement);
        
        elements.messagesWrapper.appendChild(messageElement);
      });
      
      // 滚动到底部
      setTimeout(() => {
        elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
      }, 0);
    }

    // 设置事件监听器
    function setupEventListeners() {
      elements.newChatBtn.addEventListener('click', handleNewChat);
      elements.chatList.addEventListener('click', handleChatListClick);
      elements.messageForm.addEventListener('submit', handleSendMessage);
      elements.messageInput.addEventListener('keydown', handleInputKeyDown);
      elements.messageInput.addEventListener('input', handleInputChange);
      elements.stopButton.addEventListener('click', stopStreaming);
      elements.logoutBtn.addEventListener('click', handleLogout);

      // 联网搜索事件
      elements.webSearchBtn.addEventListener('click', toggleWebSearch);
    }

    // 处理新建对话
    function handleNewChat() {
      const newChat = createNewChat();
      state.chats.unshift(newChat);
      state.currentChat = newChat.id;
      state.messages = [];
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 聚焦输入框
      elements.messageInput.focus();
    }

    // 处理聊天列表点击
    function handleChatListClick(event) {
      // 如果点击的是删除按钮
      if (event.target.closest('.delete-btn')) {
        event.stopPropagation(); // 阻止事件冒泡
        const chatId = event.target.closest('.delete-btn').dataset.chatId;
        handleDeleteChat(chatId);
        return;
      }
      
      // 如果点击的是聊天项
      const chatItem = event.target.closest('.chat-item');
      if (chatItem) {
        const chatId = chatItem.dataset.chatId;
        selectChat(chatId);
      }
    }

    // 选择对话
    function selectChat(chatId) {
      if (state.currentChat === chatId) return;
      
      const chat = state.chats.find(c => c.id === chatId);
      if (chat) {
        state.currentChat = chatId;
        state.messages = chat.messages;
        
        // 更新UI
        renderChatList();
        renderMessages();
      }
    }

    // 处理删除对话
    function handleDeleteChat(chatId) {
      // 过滤掉要删除的对话
      state.chats = state.chats.filter(chat => chat.id !== chatId);
      
      // 如果删除的是当前选中的对话
      if (state.currentChat === chatId) {
        if (state.chats.length > 0) {
          state.currentChat = state.chats[0].id;
          state.messages = state.chats[0].messages;
        } else {
          // 如果没有对话了，重置状态
          state.currentChat = null;
          state.messages = [];
        }
      }
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 如果没有对话了，显示空状态
      if (state.chats.length === 0) {
        elements.emptyState.classList.remove('hidden');
        elements.messagesWrapper.classList.add('hidden');
      }
    }

    // 处理输入框键盘事件
    function handleInputKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (elements.messageInput.value.trim() && !state.isStreaming) {
          // 直接调用发送消息函数，而不是触发表单提交事件
          handleSendMessage(event);
        }
      }
    }

    // 处理输入框变化
    function handleInputChange() {
      const inputValue = elements.messageInput.value.trim();
      elements.sendButton.disabled = !inputValue || state.isStreaming;
      
      // 自动调整高度
      elements.messageInput.style.height = 'auto';
      elements.messageInput.style.height = Math.min(elements.messageInput.scrollHeight, 200) + 'px';
    }



    // 处理发送消息
    async function handleSendMessage(event) {
      event.preventDefault();
      
      const messageText = elements.messageInput.value.trim();
      if (!messageText || state.isStreaming) return;
      
      let chatId = state.currentChat;
      let isNewChat = false;
      
      // 如果没有当前对话，创建一个新的
      if (!chatId || state.chats.length === 0) {
        const newChat = createNewChat(messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText);
        state.chats.unshift(newChat);
        state.currentChat = newChat.id;
        chatId = newChat.id;
        state.messages = [];
        isNewChat = true;
      }
      
      // 创建用户消息
      const userMessage = {
        id: Date.now(),
        content: messageText,
        role: 'user',
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表
      state.messages.push(userMessage);
      
      // 更新对话
      const currentChat = state.chats.find(c => c.id === chatId);
      if (currentChat) {
        currentChat.messages = state.messages;
        
        // 如果是新对话或第一条消息，设置标题
        if (isNewChat || currentChat.messages.length === 1) {
          currentChat.title = messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText;
        }
      }
      
      // 清空输入框
      elements.messageInput.value = '';
      elements.messageInput.style.height = 'auto';
      elements.sendButton.disabled = true;
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 创建AI消息占位
      const aiMessage = {
        id: Date.now() + 1,
        content: '',
        role: 'assistant',
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表
      state.messages.push(aiMessage);
      
      // 更新对话
      if (currentChat) {
        currentChat.messages = state.messages;
      }
      
      // 更新UI
      renderMessages();
      
      // 开始流式响应
      await streamResponse(chatId, messageText);
    }

    // 流式响应
    async function streamResponse(chatId, message) {
      state.isStreaming = true;
      elements.sendButton.classList.add('hidden');
      elements.stopButton.classList.remove('hidden');
      
      try {
        const abortController = new AbortController();
        state.controller = abortController;
        
        const response = await fetch('/api/chat/stream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Source': 'web'
          },
          credentials: 'include',
          body: JSON.stringify({
            chatId: chatId,
            message: message,
            stream: true,
            tool: elements.toolSelect.value,
            webSearch: webSearchState.enabled
          }),
          signal: abortController.signal
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            if (buffer.trim()) {
              console.warn('Remaining buffer:', buffer);
            }
            break;
          }
          
          buffer += decoder.decode(value, { stream: true });
          const eventBlocks = buffer.split('\n\n');
          buffer = eventBlocks.pop(); // 保留最后一个可能不完整的事件块

          for (const block of eventBlocks) {
            // 跳过完全空的事件块,除非它是流中唯一的块或buffer不为空
            if (!block.trim() && eventBlocks.length > 1 && buffer.length === 0) { 
                continue;
            }

            const fieldLines = block.split('\n');
            let currentEventTokenParts = [];
            let isDoneSignal = false;

            for (const fieldLine of fieldLines) {
              if (fieldLine.startsWith('data:')) {
                const dataContent = fieldLine.substring(5); // 移除 "data:"
                if (dataContent.trim() === '[DONE]') {
                  isDoneSignal = true;
                  break; // 停止处理这个block的后续行
                }
                currentEventTokenParts.push(dataContent);
              }
              // 可以根据需要忽略其他SSE字段 (event, id, retry) 和注释 (以:开头)
            }

            if (isDoneSignal) {
              if (reader && typeof reader.cancel === 'function') {
                await reader.cancel(); // 取消读取器
              }
              buffer = ''; // 清理 buffer
              return; // 直接退出 streamResponse 函数
            }
            
            if (currentEventTokenParts.length > 0) {
              const token = currentEventTokenParts.join('\n'); // 用换行符重新组合内容
              
              const lastMessage = state.messages[state.messages.length - 1];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content += token; // token 已不含data:前缀，并保留内部换行
                
                const currentChat = state.chats.find(c => c.id === chatId);
                if (currentChat) {
                  currentChat.messages = state.messages;
                }
                saveChats();
                renderMessages();
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Stream error:', error);
          
          // 更新最后一条消息，添加错误提示
          const lastMessage = state.messages[state.messages.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content += '\n[错误: 服务器响应失败，请稍后重试！]';
            
            // 更新对话
            const currentChat = state.chats.find(c => c.id === chatId);
            if (currentChat) {
              currentChat.messages = state.messages;
            }
            
            // 保存到本地存储
            saveChats();
            
            // 更新UI
            renderMessages();
          }
        }
      } finally {
        state.isStreaming = false;
        state.controller = null;
        elements.sendButton.classList.remove('hidden');
        elements.stopButton.classList.add('hidden');
        elements.sendButton.disabled = !elements.messageInput.value.trim();
      }
    }

    // 停止流式响应
    function stopStreaming() {
      if (state.controller) {
        state.controller.abort();
        state.controller = null;
        state.isStreaming = false;
        elements.sendButton.classList.remove('hidden');
        elements.stopButton.classList.add('hidden');
        elements.sendButton.disabled = !elements.messageInput.value.trim();
      }
    }

    // 保存聊天记录到本地存储
    function saveChats() {
      localStorage.setItem('chats', JSON.stringify(state.chats));
    }

    // 处理退出登录
    async function handleLogout() {
      try {
        const response = await fetch('/logout', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          // 退出成功后重定向到登录页面
          window.location.href = '/login.html';
        } else {
          console.error('Logout failed:', response.status);
        }
      } catch (error) {
        console.error('Error during logout:', error);
      }
    }

    // 初始化应用
    document.addEventListener('DOMContentLoaded', initApp);
  </script>
</body>
</html>
