<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>秒答</title>
  <script src="https://cdn.jsdelivr.net/npm/marked@4.0.18/marked.min.js"></script>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
  <link rel="icon" type="image/png" href="https://peiwan.bs2dl.yy.com/c0df986ba919482cb41e59352bd8f383.png">
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 0;
      overflow-x: hidden;
    }

    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-track {
      background: var(--bg-secondary);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-primary);
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--border-secondary);
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease;
        width: 280px !important;
      }

      .sidebar.open {
        left: 0;
      }

      .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }

      .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
      }

      .mobile-header {
        display: flex !important;
      }

      .desktop-header {
        display: none !important;
      }
    }

    @media (min-width: 769px) {
      .mobile-header {
        display: none !important;
      }

      .desktop-header {
        display: flex !important;
      }
    }

    /* 现代化的消息气泡样式 */
    .message-bubble {
      max-width: 85%;
      border-radius: var(--radius-xl);
      padding: 1rem 1.25rem;
      margin-bottom: 1rem;
      line-height: 1.6;
      backdrop-filter: blur(10px);
      border: 1px solid var(--border-primary);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }

    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
    }

    .assistant-message {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-secondary);
    }

    /* 头像样式优化 */
    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    /* Markdown 内容样式 */
    .markdown-content {
      line-height: 1.7;
    }

    .markdown-content p {
      margin-bottom: 1rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
    }

    .markdown-content li {
      margin-bottom: 0.5rem;
    }

    .markdown-content strong {
      font-weight: 600;
      color: var(--text-primary);
    }

    .markdown-content code {
      background: var(--bg-secondary);
      color: var(--accent-primary);
      padding: 0.125rem 0.375rem;
      border-radius: var(--radius-sm);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 0.875em;
      border: 1px solid var(--border-primary);
    }

    .markdown-content pre {
      background: var(--bg-secondary);
      padding: 1rem;
      border-radius: var(--radius-md);
      overflow-x: auto;
      margin: 1rem 0;
      border: 1px solid var(--border-primary);
      box-shadow: var(--shadow-sm);
    }

    .markdown-content pre code {
      background: transparent;
      padding: 0;
      border: none;
      color: var(--text-primary);
    }

    .markdown-content table {
      border-collapse: collapse;
      margin: 1rem 0;
      width: 100%;
      border-radius: var(--radius-md);
      overflow: hidden;
      border: 1px solid var(--border-primary);
    }

    .markdown-content th,
    .markdown-content td {
      border: 1px solid var(--border-primary);
      padding: 0.75rem;
      text-align: left;
    }

    .markdown-content th {
      background: var(--bg-secondary);
      font-weight: 600;
      color: var(--text-primary);
    }

    .markdown-content img {
      max-width: 100%;
      height: auto;
      border-radius: var(--radius-md);
      margin: 1rem 0;
      box-shadow: var(--shadow-sm);
    }

    .markdown-content blockquote {
      border-left: 4px solid var(--accent-primary);
      padding-left: 1rem;
      margin: 1rem 0;
      color: var(--text-secondary);
      background: var(--accent-light);
      padding: 1rem;
      border-radius: var(--radius-md);
    }
    /* 侧边栏样式 */
    .sidebar {
      background: var(--bg-secondary);
      border-right: 1px solid var(--border-primary);
      backdrop-filter: blur(20px);
    }

    .chat-item {
      border-radius: var(--radius-md);
      transition: all 0.2s ease;
      border: 1px solid transparent;
    }

    .chat-item:hover {
      background: var(--bg-hover);
      border-color: var(--border-secondary);
      transform: translateX(2px);
    }

    .chat-item.active {
      background: var(--accent-light);
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    /* Cherry Studio 风格的工具选择器 */
    .tool-selector {
      position: relative;
      margin-bottom: 0.75rem;
    }

    .tool-dropdown {
      position: absolute;
      bottom: 100%;
      left: 0;
      right: 0;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      backdrop-filter: blur(20px);
      z-index: 100;
      max-height: 200px;
      overflow-y: auto;
      opacity: 0;
      visibility: hidden;
      transform: translateY(10px);
      transition: all 0.2s ease;
    }

    .tool-dropdown.show {
      opacity: 1;
      visibility: visible;
      transform: translateY(0);
    }

    .tool-option {
      padding: 0.75rem 1rem;
      cursor: pointer;
      border-bottom: 1px solid var(--border-primary);
      transition: all 0.15s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .tool-option:last-child {
      border-bottom: none;
    }

    .tool-option:hover {
      background: var(--bg-hover);
    }

    .tool-option.selected {
      background: var(--accent-light);
      color: var(--accent-primary);
    }

    .tool-icon {
      width: 16px;
      height: 16px;
      opacity: 0.7;
    }

    .tool-name {
      font-weight: 500;
      font-size: 0.875rem;
    }

    .tool-description {
      font-size: 0.75rem;
      color: var(--text-tertiary);
      margin-top: 0.125rem;
    }

    .tool-trigger {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .tool-trigger:hover {
      border-color: var(--accent-primary);
      background: var(--accent-light);
    }

    .tool-trigger.active {
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    /* Grok风格的输入框内联网搜索按钮 */
    .input-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .web-search-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: var(--radius-md);
      background: transparent;
      border: 1px solid var(--border-secondary);
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-tertiary);
      position: relative;
      flex-shrink: 0;
    }

    .web-search-btn:hover {
      background: var(--bg-hover);
      border-color: var(--border-primary);
      color: var(--text-secondary);
    }

    .web-search-btn.active {
      background: var(--accent-light);
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    .web-search-btn.active::after {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 8px;
      height: 8px;
      background: var(--accent-primary);
      border-radius: 50%;
      border: 2px solid var(--bg-tertiary);
    }

    .web-search-icon {
      width: 16px;
      height: 16px;
    }

    /* 工具提示 */
    .tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: var(--bg-primary);
      color: var(--text-primary);
      padding: 0.5rem 0.75rem;
      border-radius: var(--radius-md);
      font-size: 0.75rem;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      z-index: 1000;
      border: 1px solid var(--border-secondary);
      box-shadow: var(--shadow-lg);
      margin-bottom: 0.5rem;
    }

    .tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: var(--border-secondary);
    }

    .web-search-btn:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }
    /* 现代化输入框样式 */
    .input-container {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-xl);
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      box-shadow: var(--shadow-sm);
    }

    .input-container:focus-within {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px var(--accent-light);
    }

    .message-input {
      background: transparent;
      border: none;
      outline: none;
      color: var(--text-primary);
      font-size: 0.95rem;
      line-height: 1.5;
      resize: none;
    }

    .message-input::placeholder {
      color: var(--text-tertiary);
    }

    /* 按钮样式优化 */
    .btn-primary {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-md);
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .btn-primary:active {
      transform: translateY(0);
    }

    .btn-primary:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .btn-secondary {
      background: var(--bg-tertiary);
      color: var(--text-secondary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-lg);
      padding: 0.75rem 1.5rem;
      font-weight: 500;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .btn-secondary:hover {
      background: var(--bg-hover);
      border-color: var(--border-primary);
      color: var(--text-primary);
    }

    .send-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin-left: 0.5rem;
    }

    .stop-btn {
      background: linear-gradient(135deg, var(--error), #dc2626);
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      margin-left: 0.5rem;
    }

    /* 优雅的打字指示器 */
    .typing-indicator {
      display: inline-flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem 0;
    }

    .typing-indicator span {
      height: 6px;
      width: 6px;
      background: var(--accent-primary);
      border-radius: 50%;
      display: inline-block;
      animation: typing-bounce 1.4s infinite ease-in-out both;
    }

    .typing-indicator span:nth-child(1) {
      animation-delay: -0.32s;
    }

    .typing-indicator span:nth-child(2) {
      animation-delay: -0.16s;
    }

    .typing-indicator span:nth-child(3) {
      animation-delay: 0s;
    }

    @keyframes typing-bounce {
      0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
      }
      40% {
        transform: scale(1.2);
        opacity: 1;
      }
    }

    /* 空状态样式 */
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      text-align: center;
      padding: 2rem;
    }

    .empty-state-icon {
      width: 64px;
      height: 64px;
      margin-bottom: 1.5rem;
      opacity: 0.3;
    }

    .empty-state-title {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: var(--text-primary);
    }

    .empty-state-description {
      color: var(--text-tertiary);
      max-width: 400px;
      line-height: 1.6;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
      .message-bubble {
        max-width: 95%;
        padding: 0.875rem 1rem;
      }

      .avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }

      .tool-dropdown {
        max-height: 150px;
      }

      .empty-state-title {
        font-size: 1.25rem;
      }

      .empty-state-description {
        font-size: 0.875rem;
      }
    }
  </style>
</head>
<body>
  <!-- Thymeleaf变量显示 -->
  <div th:if="${message}" class="hidden" id="serverMessage" th:text="${message}"></div>

  <!-- 移动端遮罩层 -->
  <div class="sidebar-overlay" id="sidebarOverlay"></div>

  <div class="flex h-screen">
    <!-- 侧边栏 -->
    <div class="w-72 sidebar p-4 flex flex-col" id="sidebar">
      <button
        id="newChatBtn"
        class="mb-6 btn-primary"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        新建对话
      </button>
      <div id="chatList" class="flex-1 overflow-y-auto space-y-2">
        <!-- 聊天列表将通过JavaScript动态生成 -->
        <div class="text-center mt-8" id="noChatMessage" style="color: var(--text-tertiary); font-size: 0.875rem;">
          没有对话记录
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 头部区域 -->
      <div class="flex justify-between items-center p-4 border-b border-opacity-10" style="border-color: var(--border-primary);">
        <!-- 移动端菜单按钮 -->
        <button
          id="mobileMenuBtn"
          class="mobile-header btn-secondary p-2"
          style="display: none;"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>

        <!-- 桌面端标题 -->
        <div class="desktop-header flex items-center">
          <h1 class="text-lg font-semibold" style="color: var(--text-primary);">秒答</h1>
        </div>

        <!-- 退出登录按钮 -->
        <button
          id="logoutBtn"
          class="btn-secondary"
          title="退出登录"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
          </svg>
          <span class="hidden sm:inline">退出登录</span>
        </button>
      </div>

      <!-- 消息区域 -->
      <div class="flex-1 overflow-y-auto p-4 md:p-6" id="messageContainer">
        <div id="emptyState" class="empty-state">
          <svg xmlns="http://www.w3.org/2000/svg" class="empty-state-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
          <h2 class="empty-state-title">开始新的对话</h2>
          <p class="empty-state-description">
            输入您的问题，开始与秒答对话。您可以询问任何问题，我会尽力为您提供帮助。
          </p>
        </div>
        <div id="messagesWrapper" class="max-w-4xl mx-auto hidden">
          <!-- 消息将通过JavaScript动态生成 -->
        </div>
      </div>
      <!-- 输入区域 -->
      <div class="p-4" style="background: var(--bg-secondary); border-top: 1px solid var(--border-primary);">
        <div class="max-w-4xl mx-auto">


          <!-- Cherry Studio 风格的工具选择器 -->
          <div class="tool-selector" id="toolSelector">
            <div class="tool-dropdown" id="toolDropdown">
              <!-- 工具选项将通过JavaScript动态生成 -->
            </div>
            <div class="tool-trigger" id="toolTrigger">
              <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
              </svg>
              <span id="toolTriggerText">选择工具</span>
              <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>

          <!-- 输入框 -->
          <form id="messageForm" class="input-container p-3 flex items-end gap-3">
            <textarea
              id="messageInput"
              class="flex-1 message-input p-2 text-base resize-none min-h-[48px] max-h-[200px]"
              placeholder="输入您的问题..."
              rows="1"
            ></textarea>
            <div class="input-actions">
              <!-- Grok风格的联网搜索按钮 -->
              <button
                type="button"
                id="webSearchBtn"
                class="web-search-btn"
                title="联网搜索"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="web-search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0 9c-1.657 0-3-4.03-3-9s1.343-9 3-9m0 9c1.657 0 3 4.03 3 9s-1.343 9-3 9m-9 9a9 9 0 019-9" />
                </svg>
                <div class="tooltip" id="webSearchTooltip">联网搜索：已关闭</div>
              </button>

              <button
                type="submit"
                id="sendButton"
                class="btn-primary send-btn"
                disabled
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </button>
              <button
                type="button"
                id="stopButton"
                class="stop-btn hidden"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z" />
                </svg>
              </button>
            </div>
          </form>
          <div class="text-xs text-center mt-2" style="color: var(--text-tertiary);">
            按 Enter 发送，Shift + Enter 换行
          </div>
        </div>
      </div>
    </div>
  </div>

  <script th:inline="javascript">
    // 配置 marked 以支持单个换行和更宽松的 Markdown 解析
    marked.setOptions({
      breaks: true, // 单个换行符视为 <br>
      gfm: true, // 启用 GitHub Flavored Markdown
      highlight: function(code, lang) {
        return code;
      }
    });

    // 获取服务器传递的消息（如果有）
    const serverMessage = document.getElementById('serverMessage')?.textContent;
    if (serverMessage) {
      console.log('Server message:', serverMessage);
    }

    // 应用状态
    const state = {
      chats: [],
      currentChat: null,
      messages: [],
      isStreaming: false,
      controller: null
    };

    // DOM元素
    const elements = {
      newChatBtn: document.getElementById('newChatBtn'),
      chatList: document.getElementById('chatList'),
      noChatMessage: document.getElementById('noChatMessage'),
      messageContainer: document.getElementById('messageContainer'),
      emptyState: document.getElementById('emptyState'),
      messagesWrapper: document.getElementById('messagesWrapper'),
      messageForm: document.getElementById('messageForm'),
      messageInput: document.getElementById('messageInput'),
      sendButton: document.getElementById('sendButton'),
      stopButton: document.getElementById('stopButton'),
      logoutBtn: document.getElementById('logoutBtn'),
      // 新的工具选择器元素
      toolSelector: document.getElementById('toolSelector'),
      toolTrigger: document.getElementById('toolTrigger'),
      toolTriggerText: document.getElementById('toolTriggerText'),
      toolDropdown: document.getElementById('toolDropdown'),
      // 联网搜索元素
      webSearchBtn: document.getElementById('webSearchBtn'),
      webSearchTooltip: document.getElementById('webSearchTooltip'),
      // 移动端元素
      mobileMenuBtn: document.getElementById('mobileMenuBtn'),
      sidebar: document.getElementById('sidebar'),
      sidebarOverlay: document.getElementById('sidebarOverlay')
    };

    // 工具状态
    const toolState = {
      tools: [],
      selectedTool: null,
      isDropdownOpen: false
    };

    // 联网搜索状态
    const webSearchState = {
      enabled: false
    };

    // 获取指定名称的 cookie 值
    function getCookie(name) {
      const nameEQ = name + "=";
      const ca = document.cookie.split(';');
      for(let i=0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
      }
      return '你';
    }

    // 创建新对话的函数
    function createNewChat(title = '新对话') {
      return {
        id: Date.now().toString(),
        title,
        messages: [],
        createdAt: new Date().toISOString()
      };
    }

    // 格式化时间
    function formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    // 初始化应用
    function initApp() {
      // 加载保存的聊天记录
      const savedChats = JSON.parse(localStorage.getItem('chats') || '[]');
      state.chats = savedChats;

      // 如果有聊天记录，选择第一个
      if (savedChats.length > 0) {
        state.currentChat = savedChats[0].id;
        state.messages = savedChats[0].messages;
      }

      // 加载联网搜索状态
      const savedWebSearchState = localStorage.getItem('webSearchEnabled');
      if (savedWebSearchState !== null) {
        webSearchState.enabled = JSON.parse(savedWebSearchState);
        updateWebSearchUI();
      }


      // 渲染UI
      renderChatList();
      renderMessages();

      // 设置事件监听器
      setupEventListeners();

      // 确保滚动到底部
      setTimeout(() => {
        elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
      }, 100);

      // 加载工具列表
      loadTools();
    }

    // 加载工具列表
    async function loadTools() {
      try {
        const response = await fetch('/api/tools');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const tools = await response.json();
        toolState.tools = tools;
        renderToolDropdown();
      } catch (error) {
        console.error('Error loading tools:', error);
        // 可以添加错误提示给用户
      }
    }

    // 渲染工具下拉框
    function renderToolDropdown() {
      elements.toolDropdown.innerHTML = '';

      // 添加"无工具"选项
      const noneOption = document.createElement('div');
      noneOption.className = `tool-option ${!toolState.selectedTool ? 'selected' : ''}`;
      noneOption.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
        <div>
          <div class="tool-name">无工具</div>
          <div class="tool-description">不使用任何工具</div>
        </div>
      `;
      noneOption.addEventListener('click', () => selectTool(null));
      elements.toolDropdown.appendChild(noneOption);

      // 添加工具选项
      toolState.tools.forEach(tool => {
        const option = document.createElement('div');
        option.className = `tool-option ${toolState.selectedTool?.name === tool.name ? 'selected' : ''}`;
        option.innerHTML = `
          <svg xmlns="http://www.w3.org/2000/svg" class="tool-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
          </svg>
          <div>
            <div class="tool-name">${tool.name}</div>
            <div class="tool-description">${tool.description}</div>
          </div>
        `;
        option.addEventListener('click', () => selectTool(tool));
        elements.toolDropdown.appendChild(option);
      });
    }

    // 选择工具
    function selectTool(tool) {
      toolState.selectedTool = tool;
      elements.toolTriggerText.textContent = tool ? tool.name : '选择工具';
      elements.toolTrigger.classList.toggle('active', !!tool);
      renderToolDropdown();
      toggleToolDropdown(false);
    }

    // 切换工具下拉框显示状态
    function toggleToolDropdown(show = null) {
      if (show === null) {
        toolState.isDropdownOpen = !toolState.isDropdownOpen;
      } else {
        toolState.isDropdownOpen = show;
      }

      elements.toolDropdown.classList.toggle('show', toolState.isDropdownOpen);
      elements.toolTrigger.classList.toggle('active', toolState.isDropdownOpen);
    }

    // 切换联网搜索状态
    function toggleWebSearch() {
      webSearchState.enabled = !webSearchState.enabled;
      updateWebSearchUI();
      saveWebSearchState();
    }

    // 更新联网搜索UI
    function updateWebSearchUI() {
      elements.webSearchBtn.classList.toggle('active', webSearchState.enabled);
      elements.webSearchTooltip.textContent = `联网搜索：${webSearchState.enabled ? '已开启' : '已关闭'}`;
      elements.webSearchBtn.title = `联网搜索：${webSearchState.enabled ? '已开启' : '已关闭'}`;
    }

    // 保存联网搜索状态
    function saveWebSearchState() {
      localStorage.setItem('webSearchEnabled', JSON.stringify(webSearchState.enabled));
    }



    // 渲染聊天列表
    function renderChatList() {
      // 清空现有列表
      elements.chatList.innerHTML = '';

      if (state.chats.length === 0) {
        // 显示"没有对话记录"消息
        elements.noChatMessage.classList.remove('hidden');
        return;
      }

      // 隐藏"没有对话记录"消息
      elements.noChatMessage.classList.add('hidden');

      // 添加新的聊天项
      state.chats.forEach(chat => {
        const chatItem = document.createElement('div');
        chatItem.className = `py-3 px-3 cursor-pointer text-sm flex justify-between items-center chat-item ${
          state.currentChat === chat.id ? 'active' : ''
        }`;
        chatItem.dataset.chatId = chat.id;
        chatItem.style.color = state.currentChat === chat.id ? 'var(--accent-primary)' : 'var(--text-secondary)';

        chatItem.innerHTML = `
          <div class="flex items-center truncate flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <span class="truncate font-medium">${chat.title}</span>
          </div>
          <button
            class="ml-2 p-1 rounded-full delete-btn opacity-0 group-hover:opacity-100 transition-opacity"
            style="color: var(--text-tertiary);"
            title="删除对话"
            data-chat-id="${chat.id}"
            onmouseover="this.style.color='var(--error)'"
            onmouseout="this.style.color='var(--text-tertiary)'"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        `;

        // 添加hover效果
        chatItem.addEventListener('mouseenter', () => {
          if (state.currentChat !== chat.id) {
            chatItem.style.color = 'var(--text-primary)';
          }
          chatItem.querySelector('.delete-btn').style.opacity = '1';
        });

        chatItem.addEventListener('mouseleave', () => {
          if (state.currentChat !== chat.id) {
            chatItem.style.color = 'var(--text-secondary)';
          }
          chatItem.querySelector('.delete-btn').style.opacity = '0';
        });

        elements.chatList.appendChild(chatItem);
      });
    }

    // 渲染消息
    function renderMessages() {
      // 清空现有消息
      elements.messagesWrapper.innerHTML = '';
      
      if (!state.messages || state.messages.length === 0) {
        // 显示空状态
        elements.emptyState.classList.remove('hidden');
        elements.messagesWrapper.classList.add('hidden');
        return;
      }
      
      // 隐藏空状态，显示消息容器
      elements.emptyState.classList.add('hidden');
      elements.messagesWrapper.classList.remove('hidden');
      
      // 添加新的消息
      state.messages.forEach(message => {
        const messageElement = document.createElement('div');
        messageElement.className = `flex flex-col mb-6 ${message.role === 'user' ? 'items-end' : 'items-start'}`;
        
        const headerElement = document.createElement('div');
        headerElement.className = `flex items-center mb-2 ${message.role === 'user' ? 'flex-row-reverse' : 'flex-row'}`;
        
        const avatarElement = document.createElement('div');
        avatarElement.className = `avatar ${
          message.role === 'user' ? 'user-avatar ml-2' : 'assistant-avatar mr-2'
        }`;
        avatarElement.textContent = message.role === 'user' ? '👤' : '🤖';
        
        const nameElement = document.createElement('div');
        nameElement.className = 'text-sm font-medium';
        if (message.role === 'user') {
          const username = getCookie('username'); // 假设用户名为 'username' 的 cookie
          nameElement.textContent = username ? username : '你';
        } else {
          nameElement.textContent = '秒答';
        }
        
        const timeElement = document.createElement('div');
        timeElement.className = `text-xs text-gray-500 ${message.role === 'user' ? 'mr-2' : 'ml-2'}`;
        timeElement.textContent = formatTime(message.timestamp);
        
        headerElement.appendChild(avatarElement);
        headerElement.appendChild(nameElement);
        headerElement.appendChild(timeElement);
        
        const bubbleElement = document.createElement('div');
        bubbleElement.className = `message-bubble ${
          message.role === 'user' ? 'user-message mr-10' : 'assistant-message ml-10'
        }`;
        
        if (message.role === 'assistant' && message.content === '' && state.isStreaming) {
          const typingIndicator = document.createElement('div');
          typingIndicator.className = 'typing-indicator';
          typingIndicator.innerHTML = '<span></span><span></span><span></span>';
          bubbleElement.appendChild(typingIndicator);
        } else if (message.role === 'assistant') {
          const contentElement = document.createElement('div');
          contentElement.className = 'markdown-content';
          contentElement.innerHTML = marked.parse(message.content);
          bubbleElement.appendChild(contentElement);
        } else {
          const contentElement = document.createElement('div');
          contentElement.className = 'user-content';
          contentElement.innerHTML = message.content.replace(/\n/g, '<br>');
          
          bubbleElement.appendChild(contentElement);
        }
        
        messageElement.appendChild(headerElement);
        messageElement.appendChild(bubbleElement);
        
        elements.messagesWrapper.appendChild(messageElement);
      });
      
      // 滚动到底部
      setTimeout(() => {
        elements.messageContainer.scrollTop = elements.messageContainer.scrollHeight;
      }, 0);
    }

    // 设置事件监听器
    function setupEventListeners() {
      elements.newChatBtn.addEventListener('click', handleNewChat);
      elements.chatList.addEventListener('click', handleChatListClick);
      elements.messageForm.addEventListener('submit', handleSendMessage);
      elements.messageInput.addEventListener('keydown', handleInputKeyDown);
      elements.messageInput.addEventListener('input', handleInputChange);
      elements.stopButton.addEventListener('click', stopStreaming);
      elements.logoutBtn.addEventListener('click', handleLogout);

      // 工具选择器事件
      elements.toolTrigger.addEventListener('click', () => toggleToolDropdown());

      // 联网搜索事件
      elements.webSearchBtn.addEventListener('click', toggleWebSearch);

      // 移动端菜单事件
      elements.mobileMenuBtn.addEventListener('click', toggleMobileSidebar);
      elements.sidebarOverlay.addEventListener('click', () => toggleMobileSidebar(false));

      // 点击外部关闭工具下拉框
      document.addEventListener('click', (event) => {
        if (!elements.toolSelector.contains(event.target)) {
          toggleToolDropdown(false);
        }
      });

      // 响应式处理
      window.addEventListener('resize', handleResize);
    }

    // 移动端侧边栏切换
    function toggleMobileSidebar(show = null) {
      const isOpen = show !== null ? show : !elements.sidebar.classList.contains('open');

      elements.sidebar.classList.toggle('open', isOpen);
      elements.sidebarOverlay.classList.toggle('show', isOpen);

      // 防止背景滚动
      document.body.style.overflow = isOpen ? 'hidden' : '';
    }

    // 响应式处理
    function handleResize() {
      if (window.innerWidth > 768) {
        // 桌面端：关闭移动端侧边栏
        toggleMobileSidebar(false);
      }
    }

    // 处理新建对话
    function handleNewChat() {
      const newChat = createNewChat();
      state.chats.unshift(newChat);
      state.currentChat = newChat.id;
      state.messages = [];
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 聚焦输入框
      elements.messageInput.focus();
    }

    // 处理聊天列表点击
    function handleChatListClick(event) {
      // 如果点击的是删除按钮
      if (event.target.closest('.delete-btn')) {
        event.stopPropagation(); // 阻止事件冒泡
        const chatId = event.target.closest('.delete-btn').dataset.chatId;
        handleDeleteChat(chatId);
        return;
      }
      
      // 如果点击的是聊天项
      const chatItem = event.target.closest('.chat-item');
      if (chatItem) {
        const chatId = chatItem.dataset.chatId;
        selectChat(chatId);
      }
    }

    // 选择对话
    function selectChat(chatId) {
      if (state.currentChat === chatId) return;
      
      const chat = state.chats.find(c => c.id === chatId);
      if (chat) {
        state.currentChat = chatId;
        state.messages = chat.messages;
        
        // 更新UI
        renderChatList();
        renderMessages();
      }
    }

    // 处理删除对话
    function handleDeleteChat(chatId) {
      // 过滤掉要删除的对话
      state.chats = state.chats.filter(chat => chat.id !== chatId);
      
      // 如果删除的是当前选中的对话
      if (state.currentChat === chatId) {
        if (state.chats.length > 0) {
          state.currentChat = state.chats[0].id;
          state.messages = state.chats[0].messages;
        } else {
          // 如果没有对话了，重置状态
          state.currentChat = null;
          state.messages = [];
        }
      }
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 如果没有对话了，显示空状态
      if (state.chats.length === 0) {
        elements.emptyState.classList.remove('hidden');
        elements.messagesWrapper.classList.add('hidden');
      }
    }

    // 处理输入框键盘事件
    function handleInputKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (elements.messageInput.value.trim() && !state.isStreaming) {
          // 直接调用发送消息函数，而不是触发表单提交事件
          handleSendMessage(event);
        }
      }
    }

    // 处理输入框变化
    function handleInputChange() {
      const inputValue = elements.messageInput.value.trim();
      elements.sendButton.disabled = !inputValue || state.isStreaming;
      
      // 自动调整高度
      elements.messageInput.style.height = 'auto';
      elements.messageInput.style.height = Math.min(elements.messageInput.scrollHeight, 200) + 'px';
    }



    // 处理发送消息
    async function handleSendMessage(event) {
      event.preventDefault();
      
      const messageText = elements.messageInput.value.trim();
      if (!messageText || state.isStreaming) return;
      
      let chatId = state.currentChat;
      let isNewChat = false;
      
      // 如果没有当前对话，创建一个新的
      if (!chatId || state.chats.length === 0) {
        const newChat = createNewChat(messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText);
        state.chats.unshift(newChat);
        state.currentChat = newChat.id;
        chatId = newChat.id;
        state.messages = [];
        isNewChat = true;
      }
      
      // 创建用户消息
      const userMessage = {
        id: Date.now(),
        content: messageText,
        role: 'user',
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表
      state.messages.push(userMessage);
      
      // 更新对话
      const currentChat = state.chats.find(c => c.id === chatId);
      if (currentChat) {
        currentChat.messages = state.messages;
        
        // 如果是新对话或第一条消息，设置标题
        if (isNewChat || currentChat.messages.length === 1) {
          currentChat.title = messageText.length > 15 ? messageText.substring(0, 15) + '...' : messageText;
        }
      }
      
      // 清空输入框
      elements.messageInput.value = '';
      elements.messageInput.style.height = 'auto';
      elements.sendButton.disabled = true;
      
      // 保存到本地存储
      saveChats();
      
      // 更新UI
      renderChatList();
      renderMessages();
      
      // 创建AI消息占位
      const aiMessage = {
        id: Date.now() + 1,
        content: '',
        role: 'assistant',
        timestamp: new Date().toISOString()
      };
      
      // 添加到消息列表
      state.messages.push(aiMessage);
      
      // 更新对话
      if (currentChat) {
        currentChat.messages = state.messages;
      }
      
      // 更新UI
      renderMessages();
      
      // 开始流式响应
      await streamResponse(chatId, messageText);
    }

    // 流式响应
    async function streamResponse(chatId, message) {
      state.isStreaming = true;
      elements.sendButton.classList.add('hidden');
      elements.stopButton.classList.remove('hidden');
      
      try {
        const abortController = new AbortController();
        state.controller = abortController;
        
        const response = await fetch('/api/chat/stream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0',
            'X-Source': 'web'
          },
          credentials: 'include',
          body: JSON.stringify({
            chatId: chatId,
            message: message,
            stream: true,
            tool: toolState.selectedTool?.name || '',
            webSearch: webSearchState.enabled
          }),
          signal: abortController.signal
        });
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        
        while (true) {
          const { value, done } = await reader.read();
          if (done) {
            if (buffer.trim()) {
              console.warn('Remaining buffer:', buffer);
            }
            break;
          }
          
          buffer += decoder.decode(value, { stream: true });
          const eventBlocks = buffer.split('\n\n');
          buffer = eventBlocks.pop(); // 保留最后一个可能不完整的事件块

          for (const block of eventBlocks) {
            // 跳过完全空的事件块,除非它是流中唯一的块或buffer不为空
            if (!block.trim() && eventBlocks.length > 1 && buffer.length === 0) { 
                continue;
            }

            const fieldLines = block.split('\n');
            let currentEventTokenParts = [];
            let isDoneSignal = false;

            for (const fieldLine of fieldLines) {
              if (fieldLine.startsWith('data:')) {
                const dataContent = fieldLine.substring(5); // 移除 "data:"
                if (dataContent.trim() === '[DONE]') {
                  isDoneSignal = true;
                  break; // 停止处理这个block的后续行
                }
                currentEventTokenParts.push(dataContent);
              }
              // 可以根据需要忽略其他SSE字段 (event, id, retry) 和注释 (以:开头)
            }

            if (isDoneSignal) {
              if (reader && typeof reader.cancel === 'function') {
                await reader.cancel(); // 取消读取器
              }
              buffer = ''; // 清理 buffer
              return; // 直接退出 streamResponse 函数
            }
            
            if (currentEventTokenParts.length > 0) {
              const token = currentEventTokenParts.join('\n'); // 用换行符重新组合内容
              
              const lastMessage = state.messages[state.messages.length - 1];
              if (lastMessage && lastMessage.role === 'assistant') {
                lastMessage.content += token; // token 已不含data:前缀，并保留内部换行
                
                const currentChat = state.chats.find(c => c.id === chatId);
                if (currentChat) {
                  currentChat.messages = state.messages;
                }
                saveChats();
                renderMessages();
              }
            }
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Stream error:', error);
          
          // 更新最后一条消息，添加错误提示
          const lastMessage = state.messages[state.messages.length - 1];
          if (lastMessage && lastMessage.role === 'assistant') {
            lastMessage.content += '\n[错误: 服务器响应失败，请稍后重试！]';
            
            // 更新对话
            const currentChat = state.chats.find(c => c.id === chatId);
            if (currentChat) {
              currentChat.messages = state.messages;
            }
            
            // 保存到本地存储
            saveChats();
            
            // 更新UI
            renderMessages();
          }
        }
      } finally {
        state.isStreaming = false;
        state.controller = null;
        elements.sendButton.classList.remove('hidden');
        elements.stopButton.classList.add('hidden');
        elements.sendButton.disabled = !elements.messageInput.value.trim();
      }
    }

    // 停止流式响应
    function stopStreaming() {
      if (state.controller) {
        state.controller.abort();
        state.controller = null;
        state.isStreaming = false;
        elements.sendButton.classList.remove('hidden');
        elements.stopButton.classList.add('hidden');
        elements.sendButton.disabled = !elements.messageInput.value.trim();
      }
    }

    // 保存聊天记录到本地存储
    function saveChats() {
      localStorage.setItem('chats', JSON.stringify(state.chats));
    }

    // 处理退出登录
    async function handleLogout() {
      try {
        const response = await fetch('/logout', {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          // 退出成功后重定向到登录页面
          window.location.href = '/login.html';
        } else {
          console.error('Logout failed:', response.status);
        }
      } catch (error) {
        console.error('Error during logout:', error);
      }
    }

    // 初始化应用
    document.addEventListener('DOMContentLoaded', initApp);
  </script>
</body>
</html>
